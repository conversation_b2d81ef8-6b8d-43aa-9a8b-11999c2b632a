{"name": "simple-streamable-http-server", "version": "1.0.0", "description": "Simple Streamable HTTP Server example from MCP TypeScript SDK", "type": "module", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "tsx src/index.ts", "dev:oauth": "tsx src/index.ts --oauth", "dev:oauth-strict": "tsx src/index.ts --oauth --oauth-strict"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.16.0", "express": "^5.0.1", "cors": "^2.8.5", "zod": "^3.23.8"}, "devDependencies": {"@types/express": "^5.0.0", "@types/cors": "^2.8.17", "@types/node": "^22.0.2", "tsx": "^4.16.5", "typescript": "^5.5.4"}, "engines": {"node": ">=18"}}