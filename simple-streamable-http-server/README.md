# Simple Streamable HTTP Server

This is a standalone implementation of the Simple Streamable HTTP Server example from the MCP TypeScript SDK. It demonstrates how to create an MCP (Model Context Protocol) server using the Streamable HTTP transport.

## Features

- **MCP Server Implementation**: Complete MCP server with tools, prompts, and resources
- **Streamable HTTP Transport**: Uses HTTP with Server-Sent Events for real-time communication
- **Session Management**: Supports session resumability and proper cleanup
- **OAuth Support**: Optional OAuth 2.0 authentication with PKCE
- **Multiple Tools**: Includes greeting tools, notification streaming, and user input collection
- **Resource Management**: Demonstrates resource registration and ResourceLink usage

## Prerequisites

This project requires Node.js 18 or higher. If you don't have Node.js installed, you can install it using one of these methods:

### Option 1: Using Homebrew (macOS)
```bash
# Install Homebrew if you don't have it
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install Node.js
brew install node
```

### Option 2: Using Node Version Manager (nvm)
```bash
# Install nvm
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash

# Restart your terminal or run:
source ~/.bashrc

# Install and use Node.js
nvm install 18
nvm use 18
```

### Option 3: Download from Official Website
Visit [nodejs.org](https://nodejs.org/) and download the LTS version for your operating system.

## Installation

1. Install dependencies:
```bash
npm install
```

2. Build the project:
```bash
npm run build
```

## Usage

### Development Mode

Run the server in development mode with hot reload:

```bash
# Basic server without OAuth
npm run dev

# Server with OAuth support
npm run dev:oauth

# Server with strict OAuth (requires resource indicators)
npm run dev:oauth-strict
```

### Production Mode

Build and run the server:

```bash
npm run build
npm start
```

## Environment Variables

- `MCP_PORT`: Port for the MCP server (default: 3000)
- `MCP_AUTH_PORT`: Port for the OAuth authorization server (default: 3001)

## Available Tools

1. **greet**: Simple greeting tool that takes a name parameter
2. **multi-greet**: Sends multiple greetings with notifications and delays
3. **collect-user-info**: Demonstrates user input collection (elicitation)
4. **start-notification-stream**: Sends periodic notifications for testing
5. **list-files**: Returns ResourceLinks to available resources

## Available Prompts

- **greeting-template**: A simple greeting prompt template

## Available Resources

- **greeting-resource**: A simple text greeting at `https://example.com/greetings/default`
- **example-file-1**: Example file at `file:///example/file1.txt`
- **example-file-2**: Example file at `file:///example/file2.txt`

## OAuth Authentication

When OAuth is enabled, the server provides:

- Authorization endpoint for OAuth flow
- Token introspection endpoint
- Protected resource metadata
- Bearer token authentication for MCP endpoints

The OAuth implementation is for demonstration purposes only and should not be used in production.

## API Endpoints

- `POST /mcp`: Main MCP endpoint for JSON-RPC requests
- `GET /mcp`: Server-Sent Events stream for real-time updates
- `DELETE /mcp`: Session termination endpoint
- `GET /.well-known/mcp_oauth_resource_metadata` (when OAuth enabled): Resource metadata

## Session Management

The server supports session resumability through:

- Session ID tracking via `Mcp-Session-Id` header
- Event store for message replay
- Automatic cleanup on session termination

## Development

The project uses TypeScript with ES modules. Key files:

- `src/index.ts`: Main server implementation
- `src/inMemoryEventStore.ts`: Event store for session resumability
- `src/demoInMemoryOAuthProvider.ts`: OAuth provider implementation

## License

This project is based on the MCP TypeScript SDK examples and follows the same MIT license.
